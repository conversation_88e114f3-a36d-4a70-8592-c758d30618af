import { prisma } from "~/db.server";
import type { Device as PrismaDevice } from "@prisma/client";
import { searchSimilar, COLLECTIONS, deleteEmbedding, indexDevice } from "./qdrant.server";

export interface Device extends PrismaDevice {}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  manufacturer?: string;
  customerId?: string;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Get all devices with pagination, filtering, and sorting
 */
export async function getDevices(
  userId: string,
  pagination: PaginationParams = {},
  filters: FilterParams = {}
): Promise<PaginatedResponse<Device>> {
  try {
    const {
      page = 1,
      pageSize = 10,
      orderBy = 'createdAt',
      orderDirection = 'desc'
    } = pagination;

    const { search, manufacturer, customerId } = filters;

    // Build where clause
    const where: any = { userId };

    // Add search filter if provided
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { model: { contains: search, mode: 'insensitive' } },
        { serialNumber: { contains: search, mode: 'insensitive' } },
        { manufacturer: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add manufacturer filter if provided
    if (manufacturer) {
      where.manufacturer = manufacturer;
    }

    // Add customer filter if provided
    if (customerId) {
      where.customerId = customerId;
    }

    // Get total count for pagination
    const totalCount = await prisma.device.count({ where });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get devices with pagination
    const devices = await prisma.device.findMany({
      where,
      orderBy: { [orderBy]: orderDirection },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
          },
        },
        serviceOrders: {
          select: {
            id: true,
            title: true,
            status: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
      },
    });

    return {
      data: devices,
      error: null,
      success: true,
      totalCount,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error('Failed to fetch devices:', error);
    return {
      data: null,
      error: 'Failed to fetch devices',
      success: false,
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
}

/**
 * Get a device by ID
 */
export async function getDeviceById(id: string, userId: string): Promise<ServiceResponse<Device>> {
  try {
    const device = await prisma.device.findUnique({
      where: { id, userId },
      include: {
        customer: true,
        serviceOrders: {
          orderBy: { createdAt: 'desc' },
        },
        telemetry: {
          orderBy: { timestamp: 'desc' },
          take: 5,
        },
        predictions: {
          orderBy: { predictionDate: 'desc' },
          take: 3,
        },
      },
    });

    if (!device) {
      return {
        data: null,
        error: 'Device not found',
        success: false
      };
    }

    return {
      data: device,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch device:', error);
    return {
      data: null,
      error: 'Failed to fetch device',
      success: false
    };
  }
}

/**
 * Create a new device
 */
export async function createDevice(
  data: Omit<Device, 'id' | 'createdAt' | 'updatedAt'>,
  userId: string
): Promise<ServiceResponse<Device>> {
  try {
    // Verify customer exists and belongs to user
    const customer = await prisma.customer.findUnique({
      where: { id: data.customerId, userId },
    });

    if (!customer) {
      return {
        data: null,
        error: 'Customer not found or access denied',
        success: false
      };
    }

    // Add userId to data
    const deviceData = {
      ...data,
      userId,
    };

    // Create device in database
    const device = await prisma.device.create({
      data: deviceData,
    });

    // Index device in Supabase for semantic search
    try {
      await indexDevice(prisma, device.id);
    } catch (indexError) {
      console.error('Failed to index device in Supabase:', indexError);
      // Don't fail the operation if indexing fails
    }

    return {
      data: device,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to create device:', error);
    return {
      data: null,
      error: 'Failed to create device',
      success: false
    };
  }
}

/**
 * Update an existing device
 */
export async function updateDevice(
  id: string,
  data: Partial<Omit<Device, 'id' | 'createdAt' | 'updatedAt'>>,
  userId: string
): Promise<ServiceResponse<Device>> {
  try {
    // Check if device exists and belongs to user
    const existingDevice = await prisma.device.findUnique({
      where: { id, userId },
    });

    if (!existingDevice) {
      return {
        data: null,
        error: 'Device not found or access denied',
        success: false
      };
    }

    // If customerId is being updated, verify the new customer exists and belongs to user
    if (data.customerId && data.customerId !== existingDevice.customerId) {
      const customer = await prisma.customer.findUnique({
        where: { id: data.customerId, userId },
      });

      if (!customer) {
        return {
          data: null,
          error: 'Customer not found or access denied',
          success: false
        };
      }
    }

    // Update device
    const updatedDevice = await prisma.device.update({
      where: { id },
      data,
    });

    // Re-index device in Supabase
    try {
      await indexDevice(prisma, id);
    } catch (indexError) {
      console.error('Failed to re-index device in Supabase:', indexError);
      // Don't fail the operation if indexing fails
    }

    return {
      data: updatedDevice,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to update device:', error);
    return {
      data: null,
      error: 'Failed to update device',
      success: false
    };
  }
}

/**
 * Delete a device
 */
export async function deleteDevice(id: string, userId: string): Promise<ServiceResponse<boolean>> {
  try {
    // Check if device exists and belongs to user
    const existingDevice = await prisma.device.findUnique({
      where: { id, userId },
    });

    if (!existingDevice) {
      return {
        data: null,
        error: 'Device not found or access denied',
        success: false
      };
    }

    // Delete device from Supabase vector storage first
    try {
      await deleteEmbedding(COLLECTIONS.DEVICES, id);
    } catch (deleteError) {
      console.error('Failed to delete device from Supabase vector storage:', deleteError);
      // Don't fail the operation if vector deletion fails
    }

    // Delete device from database
    await prisma.device.delete({
      where: { id },
    });

    return {
      data: true,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to delete device:', error);
    return {
      data: null,
      error: 'Failed to delete device',
      success: false
    };
  }
}

/**
 * Search devices semantically
 */
export async function searchDevices(query: string, userId: string, limit: number = 5): Promise<ServiceResponse<Device[]>> {
  try {
    // Search devices in Supabase
    const searchResults = await searchSimilar(COLLECTIONS.DEVICES, query, limit);

    // Extract device IDs from search results
    const deviceIds = searchResults.map(result => String(result.id));

    // Fetch full device data from database
    const devices = await prisma.device.findMany({
      where: {
        id: { in: deviceIds },
        userId,
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Sort devices in the same order as search results
    const sortedDevices = deviceIds
      .map(id => devices.find(device => device.id === id))
      .filter(Boolean) as Device[];

    return {
      data: sortedDevices,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to search devices:', error);
    return {
      data: null,
      error: 'Failed to search devices',
      success: false
    };
  }
}
