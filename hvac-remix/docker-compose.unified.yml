# Unified Docker Compose for HVAC-Remix + Agent Protocol Integration
# This configuration brings together the HVAC CRM and Agent Protocol systems

version: '3.8'

services:
  # HVAC-Remix Application
  hvac-remix:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SESSION_SECRET=${SESSION_SECRET}
      - AGENT_PROTOCOL_URL=http://agent-protocol-server:8001
      - AGENT_PROTOCOL_API_KEY=${AGENT_PROTOCOL_API_KEY}
      - BIELIK_V3_URL=http://bielik-v3-server:11434
      - GEMMA4_URL=http://gemma4-server:11434
      - GEMMA3_HF_URL=http://gemma3-hf-service:8000
      - ENABLE_AGENT_INTEGRATION=true
    depends_on:
      - agent-protocol-server
      - postgres
      - redis
      - qdrant
    networks:
      - hvac-network
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped

  # Agent Protocol Server
  agent-protocol-server:
    build:
      context: ./agent-protocol/server
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=********************************************/agent_protocol
      - REDIS_URL=redis://redis:6379
      - QDRANT_URL=http://qdrant:6333
      - BIELIK_V3_URL=http://bielik-v3-server:11434
      - GEMMA4_URL=http://gemma4-server:11434
      - GEMMA3_HF_URL=http://gemma3-hf-service:8000
      - PYTHONPATH=/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
      qdrant:
        condition: service_started
    networks:
      - hvac-network
    volumes:
      - ./agent-protocol/server/ap_server:/app/ap_server
      - ./agent-protocol/models:/app/models
      - agent-data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database (shared)
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=agent_protocol
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_MULTIPLE_DATABASES=hvac_remix,agent_protocol
    networks:
      - hvac-network
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./agent-protocol/server/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (shared)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - hvac-network
    volumes:
      - redis-data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Qdrant Vector Database (shared)
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    networks:
      - hvac-network
    volumes:
      - qdrant-data:/qdrant/storage
    restart: unless-stopped

  # Bielik V3 LLM Server (Polish)
  bielik-v3-server:
    image: ollama/ollama:latest
    ports:
      - "8877:11434"
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS=/root/.ollama/models
    networks:
      - hvac-network
    volumes:
      - bielik-models:/root/.ollama
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Gemma4 LLM Server
  gemma4-server:
    image: ollama/ollama:latest
    ports:
      - "8878:11434"
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS=/root/.ollama/models
    networks:
      - hvac-network
    volumes:
      - gemma4-models:/root/.ollama
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Gemma-3-4b-it HuggingFace Service (128K context)
  gemma3-hf-service:
    build:
      context: ./agent-protocol/gemma3-hf-service
      dockerfile: Dockerfile
    ports:
      - "8879:8000"
    environment:
      - MODEL_NAME=google/gemma-2-2b-it
      - MAX_CONTEXT_LENGTH=131072
      - DEVICE=cuda
      - TORCH_DTYPE=float16
    networks:
      - hvac-network
    volumes:
      - gemma3-cache:/app/cache
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    networks:
      - hvac-network
    volumes:
      - ./nginx/unified.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - hvac-remix
      - agent-protocol-server
    restart: unless-stopped

  # LLM Model Manager (initialization service)
  llm-model-manager:
    build:
      context: ./agent-protocol/llm-manager
      dockerfile: Dockerfile
    environment:
      - BIELIK_V3_URL=http://bielik-v3-server:11434
      - GEMMA4_URL=http://gemma4-server:11434
      - GEMMA3_HF_URL=http://gemma3-hf-service:8000
    networks:
      - hvac-network
    depends_on:
      - bielik-v3-server
      - gemma4-server
      - gemma3-hf-service
    restart: "no"

  # Data Sync Service (background service)
  data-sync-service:
    build:
      context: .
      dockerfile: Dockerfile.sync
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - AGENT_PROTOCOL_URL=http://agent-protocol-server:8001
      - AGENT_PROTOCOL_API_KEY=${AGENT_PROTOCOL_API_KEY}
    networks:
      - hvac-network
    depends_on:
      - hvac-remix
      - agent-protocol-server
      - postgres
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    command: ["node", "sync-service.js"]

networks:
  hvac-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
  qdrant-data:
  bielik-models:
  gemma4-models:
  gemma3-cache:
  agent-data:
