#!/usr/bin/env node

/**
 * Data Sync Service
 * Handles real-time synchronization between Supabase and Agent Protocol
 */

const { realTimeSync } = require('./build/server/app/services/database-sync.server.js');

async function startSyncService() {
  console.log('🔄 Starting Data Sync Service...');
  
  try {
    // Initialize real-time synchronization
    await realTimeSync.initializeRealTimeSync();
    
    // Perform initial sync
    await realTimeSync.performInitialSync();
    
    console.log('✅ Data Sync Service started successfully');
    
    // Keep the service running
    process.on('SIGINT', () => {
      console.log('🛑 Data Sync Service shutting down...');
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('🛑 Data Sync Service shutting down...');
      process.exit(0);
    });
    
    // Keep alive
    setInterval(() => {
      console.log('💓 Data Sync Service heartbeat');
    }, 60000); // Every minute
    
  } catch (error) {
    console.error('❌ Failed to start Data Sync Service:', error);
    process.exit(1);
  }
}

// Start the service
startSyncService();
