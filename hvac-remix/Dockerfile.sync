# Dockerfile for Data Sync Service
# This service handles real-time synchronization between Supabase and Agent Protocol

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Build the application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S remix -u 1001

# Create directories and set permissions
RUN mkdir -p /app/logs && chown -R remix:nodejs /app

# Switch to non-root user
USER remix

# Expose port (not used for sync service, but good practice)
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD node -e "console.log('Sync service healthy')" || exit 1

# Start the sync service
CMD ["node", "build/sync-service.js"]
